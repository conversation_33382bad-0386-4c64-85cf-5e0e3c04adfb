[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "==4.2.16"
django-environ = "==0.11.2"
psycopg2-binary = "==2.9.10"
django-bootstrap-pagination = "==1.7.1"
django-bootstrap3 = "==24.3"
jsonschema = "==4.23.0"
pycryptodome = "==3.21.0"
django-extensions = "==3.2.3"
python-dotenv = "==1.0.1"
Pillow="==11.0.0"
phonenumbers = "==8.13.50"
twilio = "==9.3.7"
python-decouple = "==3.8"
channels = {extras = ["daphne"], version = "4.2.0"}
channels-redis = "==4.2.1"
django-pwa = "==2.0.1"
django-webpush = "==0.3.6"
django-db-geventpool = "==4.0.8"
gunicorn = "==23.0.0"
gevent = "==25.4.1"
uvicorn = {extras = ["standard"], version = "==0.22.0"}
django-redis = "==5.4.0"
bs4 = "==0.0.2"

[dev-packages]

[requires]
python_version = "3.11"

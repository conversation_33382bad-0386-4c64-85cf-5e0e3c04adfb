"""
Custom test runner for Django that properly handles database connections with PgBouncer.
"""
import logging
import time
import psycopg2
from django.db import connections
from django.test.runner import DiscoverRunner
from django.conf import settings

logger = logging.getLogger('app')

class PgBouncerTestRunner(DiscoverRunner):
    """
    Custom test runner that properly handles database connections with PgBouncer.

    This runner:
    1. Terminates existing connections to the test database before setup
    2. Uses direct PostgreSQL connections for test database operations
    3. <PERSON><PERSON>ly closes all connections before dropping the test database

    This helps prevent the "database is being accessed by other users" error when using PgBouncer.
    """

    def setup_databases(self, **kwargs):
        """
        Override the setup_databases method to terminate any existing connections
        to the test database before creating it.
        """
        # Terminate any existing connections to the test database
        self._terminate_test_db_connections()

        # Call the parent method to set up the databases
        return super().setup_databases(**kwargs)

    def _terminate_test_db_connections(self):
        """
        Terminate all connections to the test database using a direct PostgreSQL connection.
        This is a more aggressive approach to ensure all connections are closed.
        """
        try:
            # Get database settings
            db_settings = settings.DATABASES['default']
            test_db_name = db_settings.get('TEST', {}).get('NAME', 'test_whiskers')

            # Connect directly to PostgreSQL (not through PgBouncer)
            # We need to connect to a different database than the one we want to terminate connections to
            conn = psycopg2.connect(
                dbname='postgres',  # Connect to the default postgres database
                user=db_settings['USER'],
                password=db_settings['PASSWORD'],
                host=db_settings['HOST'],
                port=db_settings['PORT'],
            )
            conn.autocommit = True
            cursor = conn.cursor()

            # SQL to terminate all connections to the test database
            sql = f"""
            SELECT pg_terminate_backend(pg_stat_activity.pid)
            FROM pg_stat_activity
            WHERE pg_stat_activity.datname = '{test_db_name}'
            AND pid <> pg_backend_pid();
            """
            cursor.execute(sql)

            # Close the connection
            cursor.close()
            conn.close()

            # Wait a moment to ensure connections are fully terminated
            time.sleep(2)

            logger.info(f"Terminated all connections to {test_db_name}")
            return True
        except Exception as e:
            logger.error(f"Error terminating connections to test database: {e}")
            return False

    def teardown_databases(self, old_config, **kwargs):
        """
        Close all database connections before dropping the test database.
        """
        # Close all Django connections first
        for connection in connections.all():
            connection.close()

        # Wait a moment to ensure connections are fully closed
        time.sleep(1)

        # Force close any remaining connections by setting CONN_MAX_AGE to 0
        for alias in connections:
            connections[alias].settings_dict['CONN_MAX_AGE'] = 0

        # Try to close connections again
        for connection in connections.all():
            connection.close()

        # Terminate all connections to the test database
        self._terminate_test_db_connections()

        # Now call the parent method to drop the database
        return super().teardown_databases(old_config, **kwargs)

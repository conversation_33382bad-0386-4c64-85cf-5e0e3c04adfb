#asset,
#battery,
#temperature,
#latitude,
#field_id,
#longitude {
    display: none;
}

.clickable {
    cursor: pointer;
    transition: background-color 0.2s ease;
    padding: 3px;
    padding-top: 7px;
    padding-bottom: 7px;
    border-radius: 3px;
}

.errorlist {
    list-style: none;
    color: #fa6767;
    padding-left: 0;
    margin-top: 0;
    margin-bottom: 0;
}

.errorlist li {
    margin-bottom: 5px;
    /* Add some spacing between error messages */
}

.custom-progress {
    background-color: #c5d3e2
}

.clickable:hover {
    background-color: #f6f6f6;
}

#centered-td-number {
    text-align: center;
}

.font-32 {
    font-size: 32px !important;
}

.dt-scrollable-cell {
    overflow-x: scroll;
    white-space: nowrap;
    height: 100%;
    /* set height to ensure scrollbar appears */
}

.checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

body {
    background-color: black;
    font-family: cursive;
}

.glow-danger {
    animation: glow-danger 0.5s ease-in-out infinite alternate;
}

@keyframes glow-danger {
    from {
        text-shadow: 0 0 10px #ff8f8f, 0 0 20px #ff8f8f, 0 0 30px #fc6767, 0 0 40px #fc6767, 0 0 50px #fd6f6f, 0 0 60px #fd9999, 0 0 70px #f79191;
    }

    to {
        text-shadow: 0 0 20px #ff8f8f, 0 0 30px #ff8f8f, 0 0 40px #ffffff, 0 0 50px #ffffff, 0 0 60px #ffffff, 0 0 70px #ffffff, 0 0 80px #ffffff;
    }
}

.glow-warning {
    animation: glow-warning 0.5s ease-in-out infinite alternate;
}

@keyframes glow-warning {
    from {
        text-shadow: 0 0 10px #ffdc8f, 0 0 20px #ffdc8f, 0 0 30px #fcd467, 0 0 40px #fcd467, 0 0 50px #fddc6f, 0 0 60px #fde299, 0 0 70px #f7ce91;
    }

    to {
        text-shadow: 0 0 20px #ffdc8f, 0 0 30px #ffdc8f, 0 0 40px #ffffff, 0 0 50px #ffffff, 0 0 60px #ffffff, 0 0 70px #ffffff, 0 0 80px #ffffff;
    }
}
#logo {
    max-width: 90% !important;
}
.filter-green{
    filter: invert(48%) sepia(79%) saturate(2476%) hue-rotate(86deg) brightness(118%) contrast(119%);
}
.filter-white{
    filter: brightness(1.3);
}
.filter-gray{
    filter: invert(82%) sepia(5%) saturate(174%) hue-rotate(174deg) brightness(88%) contrast(89%);
}
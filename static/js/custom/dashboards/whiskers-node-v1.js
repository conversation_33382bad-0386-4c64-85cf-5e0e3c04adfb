$(window).on('load', function () {
    const wsScheme = window.location.protocol === "https:" ? "wss" : "ws";
    const deviceWsPath = `${wsScheme}://${window.location.host}/ws/device/`;

    const deviceDataSocket = new ReconnectingWebSocket(deviceWsPath);
    let lastUpdateTimestamp = null; // To store the last update timestamp

    function calculateSignalPercentage(rssi) {
        // Assuming RSSI values range from 0 to -124
        const minRssi = -124;
        const maxRssi = 0;
        const signalRange = maxRssi - minRssi;
        const signalPercentage = ((rssi - minRssi) / signalRange) * 100;
        return Math.round(signalPercentage) + "%";
    }

    deviceDataSocket.onmessage = function (event) {
        const data = JSON.parse(event.data);

        // get object from first key in the json
        const deviceId = Object.keys(data)[0];
        const device = data[Object.keys(data)[0]];

        // Update chart data and options
        batteryIndicator.updateSeries([device.attr.client["Battery"]]);
        batteryIndicator.updateOptions({
            colors: device.batt <= 25 ? ['#fa6767'] : device.attr.client["Battery"] <= 50 ? ['#f9bc0d'] : ['#42d29d'],
        });

        thermometer.updateSeries([parseInt(device.attr.client["Air Temperature"])]);
        thermometer.updateOptions({
            colors: parseInt(device.attr.client["Air Temperature"]) <= 60 ? ['#3688fc'] : ['#fa6767'],
        });

        // update packets transmitted, received, corrupted, and acknowledgement rate
        document.querySelector('#frame-counter').innerHTML = device.attr.client["Frame Counter"];
        document.querySelector('#light').innerHTML = device.attr.client["Light"] + "%";
        document.querySelector('#signal').innerHTML = calculateSignalPercentage(device.attr.client["RSSI"]);

        // Update the last update timestamp and start the counter
        lastUpdateTimestamp = new Date(device.lupd);
        document.querySelector('#last-update').innerHTML = formatTimeElapsed(lastUpdateTimestamp);

        let activityElement = document.querySelector('#activity');
        activityElement.classList.remove('text-danger', 'text-warning', 'glow-danger', 'glow-warning');

        if (device.attr.client["Frame Counter"] == 0) {
            activityElement.innerHTML = "Never Reported";
        } else if (device.stat == "Offline" && device.attr.client["Frame Counter"] > 0) {
            activityElement.innerHTML = "Offline";
        } else if (device.mntc) {
            activityElement.classList.add('text-warning', 'glow-warning');
            activityElement.innerHTML = "🚧 Maintenance 🚧";
        } else if (device.actv) {
            activityElement.classList.add('text-danger', 'glow-danger');
            activityElement.innerHTML = "Moving";
        } else {
            activityElement.innerHTML = "Stationary";
        }

        updateMarker(deviceId, device.name, device.loca.lati, device.loca.long, device.aset, device.mntc, device.stat, device.speed || 0, device.direction || 0);
    };

    deviceDataSocket.onclose = function (event) {
        let connectionElement = document.querySelector('#ws-connection');
        connectionElement.innerHTML = 'DISCONNECTED';
        connectionElement.classList.remove('bg-success');
        connectionElement.classList.add('bg-secondary');

        console.log('WebSocket connection closed. Code:', event.code, 'Reason:', event.reason);
        // ReconnectingWebSocket will automatically try to reconnect
    }

    deviceDataSocket.onerror = function (error) {
        console.error('WebSocket error:', error);
    }

    deviceDataSocket.onopen = function () {
        try {
            deviceDataSocket.send(JSON.stringify({ "devices": deviceIds }));

            let connectionElement = document.querySelector('#ws-connection');
            connectionElement.innerHTML = 'CONNECTED';
            connectionElement.classList.add('bg-success');
            connectionElement.classList.remove('bg-secondary');

            console.log('WebSocket connection established');
        } catch (error) {
            console.error('Error during WebSocket open handler:', error);
        }
    }

    // Function to update the time elapsed every second
    setInterval(function () {
        if (lastUpdateTimestamp) {
            document.querySelector('#last-update').innerHTML = formatTimeElapsed(lastUpdateTimestamp);
        }
    }, 1000);
});

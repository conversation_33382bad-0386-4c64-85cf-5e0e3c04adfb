// Debug mode flag - set to false for production to improve performance
window.mapDebugMode = false;

// Flag to control whether the map should automatically zoom to fit all markers when a device updates
// Set to false to disable automatic zooming when device status changes
window.ENABLE_AUTO_ZOOM = false;

let map;
let infowindow;
let fields = {};
let markers = {};
let infoWindows = {};
let markerCluster = null;
let clusterRadius = 3; // Default cluster radius in kilometers

// Store device statuses for cluster coloring
let deviceStatuses = {};

// Cache for marker icons to improve performance
const markerIconCache = {};

// Cache for the custom renderer to avoid recreating it
let cachedCustomRenderer = null;

// Cache for the clustering algorithm
let cachedClusterAlgorithm = null;

// Cache for marker adapters to avoid recreating them
const markerAdapterCache = {};

// Function to invalidate the renderer cache
function invalidateRendererCache() {
    if (window.mapDebugMode) {
        console.log("Invalidating renderer cache");
    }
    cachedCustomRenderer = null;
    cachedClusterAlgorithm = null;
}

// Function to clear the marker adapter cache
function clearMarkerAdapterCache() {
    if (window.mapDebugMode) {
        console.log("Clearing marker adapter cache");
    }
    // Clear the marker adapter cache
    for (const key in markerAdapterCache) {
        delete markerAdapterCache[key];
    }
}

// Throttling variables for performance optimization
let clusterUpdatePending = false;
let clusterUpdateTimeout = null;
const CLUSTER_UPDATE_THROTTLE_MS = 500; // Throttle cluster updates to once per 500ms

// Zoom level at which individual markers become visible
const MARKER_VISIBILITY_ZOOM = 13;

let mapInitialized = false;
let currentZoom = 8; // Default zoom level

let AdvancedMarkerElement;
let InfoWindowElement;

async function initMap() {
    // Request needed libraries.
    //@ts-ignore
    const { Map } = await google.maps.importLibrary("maps");
    const { AdvancedMarkerElement: AME } = await google.maps.importLibrary("marker");

    // Assign the imported libraries to global variables
    AdvancedMarkerElement = AME;

    map = new Map(document.getElementById('device_map'), {
        center: { lat: 0, lng: 0 },
        zoom: 8,
        mapId: "DETAIL"
    });

    // Store the initial zoom level
    currentZoom = map.getZoom();

    // Add zoom change listener to update marker visibility
    map.addListener('zoom_changed', function() {
        currentZoom = map.getZoom();

        // Update zoom level display in the UI
        const zoomDisplay = document.getElementById('current-zoom-display');
        if (zoomDisplay) {
            zoomDisplay.textContent = currentZoom.toString();
        }

        updateMarkerVisibility();
    });

    // Initialize with default cluster radius from the UI
    if (typeof defaultClusterRadius !== 'undefined') {
        clusterRadius = defaultClusterRadius;
    }

    // Add debug mode toggle for development
    const debugModeToggle = document.getElementById('debug-mode-toggle');
    if (debugModeToggle) {
        debugModeToggle.addEventListener('change', function() {
            window.mapDebugMode = this.checked;
            console.log(`Debug mode ${window.mapDebugMode ? 'enabled' : 'disabled'}`);
        });

        // Initialize the toggle state
        debugModeToggle.checked = window.mapDebugMode;
    }

    // Add auto-zoom toggle
    const autoZoomToggle = document.getElementById('auto-zoom-toggle');
    if (autoZoomToggle) {
        autoZoomToggle.addEventListener('change', function() {
            window.ENABLE_AUTO_ZOOM = this.checked;
            console.log(`Auto-zoom on updates ${window.ENABLE_AUTO_ZOOM ? 'enabled' : 'disabled'}`);
        });

        // Initialize the toggle state
        autoZoomToggle.checked = window.ENABLE_AUTO_ZOOM;
    }

    // Add clear cache button for development
    const clearCacheButton = document.getElementById('clear-cache-button');
    if (clearCacheButton) {
        clearCacheButton.addEventListener('click', function() {
            console.log("Clearing all caches");

            // Clear all caches
            invalidateRendererCache();
            clearMarkerAdapterCache();

            // Clear marker icon cache
            for (const key in markerIconCache) {
                delete markerIconCache[key];
            }

            // Force a complete rebuild of the marker clusters
            if (markerCluster) {
                try {
                    markerCluster.setMap(null);
                    markerCluster = null;
                } catch (e) {
                    console.warn("Error removing existing marker cluster:", e);
                }
            }

            // Update markers with the new radius
            updateMarkerCluster();

            console.log("All caches cleared");
        });
    }

    // Set up event listener for cluster radius input
    const clusterRadiusInput = document.getElementById('cluster-radius');
    if (clusterRadiusInput) {
        // Use 'input' event for real-time updates as the slider moves
        clusterRadiusInput.addEventListener('input', function() {
            // Parse the new radius value
            const newRadius = parseFloat(this.value);

            // Update the UI to show the new radius in real-time
            const radiusDisplay = document.getElementById('radius-value-display');
            if (radiusDisplay) {
                radiusDisplay.textContent = newRadius.toFixed(1);
            }
        });

        // Use 'change' event for when the slider is released
        clusterRadiusInput.addEventListener('change', function() {
            // Parse the new radius value
            const newRadius = parseFloat(this.value);

            // Log the change
            console.log(`Cluster radius changed from ${clusterRadius} km to ${newRadius} km`);

            // Update the global variable
            clusterRadius = newRadius;

            // Update the UI to show the new radius
            const radiusDisplay = document.getElementById('radius-value-display');
            if (radiusDisplay) {
                radiusDisplay.textContent = clusterRadius.toFixed(1);
            }

            // Invalidate the renderer cache since the radius has changed
            invalidateRendererCache();

            // Force a complete rebuild of the marker clusters
            if (markerCluster) {
                try {
                    markerCluster.setMap(null);
                    markerCluster = null;
                } catch (e) {
                    console.warn("Error removing existing marker cluster:", e);
                }
            }

            // Update markers with the new radius
            updateMarkerCluster();
        });

        // Initialize the display value
        if (document.getElementById('radius-value-display')) {
            document.getElementById('radius-value-display').textContent = clusterRadius.toFixed(1);
        }
    }

    // Set up event listener for custom icons toggle
    const customIconsToggle = document.getElementById('use-custom-icons');
    if (customIconsToggle) {
        customIconsToggle.addEventListener('change', function() {
            // Invalidate the renderer cache since the icon style has changed
            invalidateRendererCache();

            // Update marker clusters when the toggle changes
            updateMarkerCluster();
        });
    }

    // Set up event listener for marker visibility zoom threshold
    const markerVisibilityZoom = document.getElementById('marker-visibility-zoom');
    if (markerVisibilityZoom) {
        markerVisibilityZoom.addEventListener('change', function() {
            // Update the visibility threshold
            const newThreshold = parseInt(this.value, 10);
            if (!isNaN(newThreshold)) {
                // Update the constant
                window.MARKER_VISIBILITY_ZOOM = newThreshold;

                // Update marker visibility based on the new threshold
                updateMarkerVisibility();

                console.log(`Marker visibility threshold updated to zoom level ${newThreshold}`);
            }
        });
    }

    mapInitialized = true;
    console.log("Map initialized successfully");

    // Initialize marker cluster after a short delay to ensure the map is fully loaded
    setTimeout(() => {
        // Update marker visibility based on initial zoom level
        updateMarkerVisibility();

        // Initialize marker clustering
        updateMarkerCluster();

        // Log that initialization is complete
        console.log("Map initialization complete, markers should now be visible");
    }, 2000);
}

function updatePolygon(fieldObject) {
    if (fieldObject.id in fields) {
        fields[fieldObject.id].setPaths(fieldObject.cord);
        fields[fieldObject.id].setOptions({
            strokeColor: fieldObject.colr,
            fillColor: fieldObject.colr,
        });
    } else {
        let field = new google.maps.Polygon({
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillOpacity: 0.35,
            editable: false,
            draggable: false
        });
        field.setMap(map);
        field.setPaths(fieldObject.cord);
        field.setOptions({
            strokeColor: fieldObject.colr,
            fillColor: fieldObject.colr,
        });

        fields[fieldObject.id] = field;
    }

    // recalculate map bounds
    recalcBounds();
}

function updateMarker(id, name, latitude, longitude, asset, maintenance, status) {
    // Make sure the map is initialized
    if (!mapInitialized || !map) {
        console.error("Map not initialized yet, cannot update marker");
        // Queue the update for when the map is ready
        setTimeout(() => {
            updateMarker(id, name, latitude, longitude, asset, maintenance, status);
        }, 1000);
        return;
    }

    // Validate input parameters
    if (id === undefined || id === null) {
        console.error("Invalid marker ID:", id);
        return;
    }

    // Validate latitude and longitude
    if (isNaN(parseFloat(latitude)) || isNaN(parseFloat(longitude))) {
        console.error(`Invalid coordinates for marker ${id}: lat=${latitude}, lng=${longitude}`);
        return;
    }

    // Convert latitude and longitude to numbers to ensure they're valid
    latitude = parseFloat(latitude);
    longitude = parseFloat(longitude);

    // Log marker update only in debug mode
    if (window.mapDebugMode) {
        console.log(`Updating marker ${id} (${name}) at position ${latitude}, ${longitude} with status ${status}`);
    }

    // Store the device status for cluster coloring
    deviceStatuses[id] = { status, maintenance };

    function updateMarkerIcon() {
        // select marker icon based on device status
        var icon = asset.toLowerCase().replace(/\s+/g, '-');
        if (maintenance) {
            icon = icon + "-maintenance";
        } else {
            if (status == "Online") {
                icon = icon + "-success";
            } else if (status == "Offline") {
                icon = icon + "-offline";
            } else if (status == "Warning") {
                icon = icon + "-warning";
            } else {
                icon = icon + "-danger";
            }
        }

        // Create the icon path
        const iconPath = '/static/images/device/icons/' + icon + '.png';

        // Log only in debug mode
        if (window.mapDebugMode) {
            console.log(`Creating icon for marker ${id} with status ${status}, icon path: ${iconPath}`);
        }

        // Always create a fresh icon to ensure proper updating
        const markerIcon = document.createElement("img");
        markerIcon.src = iconPath;
        markerIcon.width = 48;

        // Add a timestamp to force browser to reload the image
        markerIcon.setAttribute('data-timestamp', new Date().getTime());

        return markerIcon;
    }

    // Cache for info window templates to improve performance
    const infoWindowTemplate = `
        <div id="customInfoWindow" style="
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 1.1em;
            position: relative;">
            <span style="font-weight: bold;">Name:     </span> <span>{{name}}</span><br>
            <span style="font-weight: bold;">Latitude: </span> <span>{{latitude}}</span><br>
            <span style="font-weight: bold;">Longitude:</span> <span>{{longitude}}</span>
        </div>
    `;

    function updateInfoWindow() {
        // Replace placeholders with actual values
        return infoWindowTemplate
            .replace('{{name}}', name)
            .replace('{{latitude}}', latitude)
            .replace('{{longitude}}', longitude);
    }

    // Check if we need to update the marker cluster
    let needsClusterUpdate = false;

    // if marker id in markers, update marker
    if (id in markers) {
        // Check if position has changed before updating
        const currentMarker = markers[id];
        const positionChanged = !currentMarker.position ||
                               currentMarker.position.lat !== latitude ||
                               currentMarker.position.lng !== longitude;

        // Only update position if it has changed
        if (positionChanged) {
            currentMarker.position = { lat: latitude, lng: longitude };
            needsClusterUpdate = true;
        }

        // Check if status has changed before updating icon
        const currentMarkerStatus = {
            status: markers[id].currentStatus?.status || '',
            maintenance: markers[id].currentStatus?.maintenance || false
        };
        const statusChanged = currentMarkerStatus.status !== status ||
                             currentMarkerStatus.maintenance !== maintenance;

        // Only update icon if status has changed
        if (statusChanged) {
            // Log only in debug mode
            if (window.mapDebugMode) {
                console.log(`Status changed for marker ${id} from ${markers[id].currentStatus ? markers[id].currentStatus.status : 'unknown'} to ${status}, updating icon`);
            }

            // Get the new icon
            const newIcon = updateMarkerIcon();

            // Update the marker's content
            currentMarker.content = newIcon;

            // Update the marker's current status
            markers[id].currentStatus = { status, maintenance };

            // Force a refresh of the marker
            if (currentMarker.map) {
                // Briefly remove and re-add the marker to force a refresh
                const currentMap = currentMarker.map;
                currentMarker.map = null;
                setTimeout(() => {
                    currentMarker.map = currentMap;
                }, 10);
            }
        }

        // Only update info window content if needed
        // This is a performance optimization to avoid unnecessary DOM operations
        if (positionChanged) {
            infoWindows[id].content = updateInfoWindow();
        }
    }
    // else create new marker
    else {
        // Get the current visibility threshold (may have been updated via UI)
        const visibilityThreshold = window.MARKER_VISIBILITY_ZOOM || MARKER_VISIBILITY_ZOOM;

        // Make sure AdvancedMarkerElement is available
        if (!AdvancedMarkerElement) {
            console.error("AdvancedMarkerElement not initialized yet, cannot create marker");
            // Queue the update for when the AdvancedMarkerElement is ready
            setTimeout(() => {
                updateMarker(id, name, latitude, longitude, asset, maintenance, status);
            }, 1000);
            return;
        }

        // Create the icon
        const newIcon = updateMarkerIcon();

        console.log(`Creating new marker for device ${id} with status ${status}`);

        // Create marker but only add to map if zoom level is high enough
        let marker = new AdvancedMarkerElement({
            map: currentZoom >= visibilityThreshold ? map : null,
            position: { lat: latitude, lng: longitude },
            content: newIcon,
            title: name,
        });

        let infoWindow = new google.maps.InfoWindow({
            content: updateInfoWindow(),
        });

        // Use a more efficient event listener
        marker.addListener('click', function () {
            // Close any open info windows first to improve performance
            for (const id in infoWindows) {
                infoWindows[id].close();
            }
            infoWindow.open(map, marker);
        });

        markers[id] = marker;
        infoWindows[id] = infoWindow;
        needsClusterUpdate = true;
    }

    // Add a flag to control whether auto-zoom should happen
    // Only auto-zoom when position changes, not when just status changes
    // For new markers, we consider it a position change
    const autoZoom = (typeof positionChanged !== 'undefined' ? positionChanged : needsClusterUpdate) && window.ENABLE_AUTO_ZOOM !== false;

    // recalculate map bounds, but only auto-zoom if position changed
    recalcBounds(autoZoom);

    // If we need to update the marker cluster, do it now
    if (needsClusterUpdate) {
        // We'll recreate the marker cluster with the updated markers
        // This will be throttled to improve performance
        updateMarkerCluster();
    }
}

// Throttling variables for bounds recalculation
let boundsUpdatePending = false;
let boundsUpdateTimeout = null;
const BOUNDS_UPDATE_THROTTLE_MS = 500; // Throttle bounds updates to once per 500ms

function recalcBounds(autoZoom = true) {
    // Throttle updates for better performance
    if (boundsUpdatePending) {
        // If an update is already pending, clear the timeout and set a new one
        clearTimeout(boundsUpdateTimeout);
    }

    // Set a flag to indicate an update is pending
    boundsUpdatePending = true;

    // Schedule the update after a short delay to batch multiple updates together
    boundsUpdateTimeout = setTimeout(() => {
        // Reset the pending flag
        boundsUpdatePending = false;

        // Perform the actual update
        performBoundsUpdate(autoZoom);
    }, BOUNDS_UPDATE_THROTTLE_MS);
}

function performBoundsUpdate(autoZoom = true) {
    // Calculate the bounds of the polygon and get its center
    const bounds = new google.maps.LatLngBounds();

    // Track if we've added anything to the bounds
    let boundsChanged = false;

    // Add all field polygons to the bounds
    for (const fieldId in fields) {
        const field = fields[fieldId];
        if (field && field.getPath) {
            const path = field.getPath();
            if (path) {
                path.forEach(latLng => {
                    bounds.extend(latLng);
                    boundsChanged = true;
                });
            }
        }
    }

    // Add all markers to the bounds - limit to a reasonable number for performance
    const markerIds = Object.keys(markers);
    const maxMarkersForBounds = 100; // Only use up to 100 markers for bounds calculation

    // If we have too many markers, sample them
    if (markerIds.length > maxMarkersForBounds) {
        // Sample markers evenly across the array
        const step = Math.floor(markerIds.length / maxMarkersForBounds);
        for (let i = 0; i < markerIds.length; i += step) {
            const marker = markers[markerIds[i]];
            if (marker && marker.position) {
                bounds.extend(marker.position);
                boundsChanged = true;
            }
        }
    } else {
        // Use all markers if we have a reasonable number
        for (const markerId in markers) {
            const marker = markers[markerId];
            if (marker && marker.position) {
                bounds.extend(marker.position);
                boundsChanged = true;
            }
        }
    }

    // Only proceed if we have bounds to fit and they've changed
    if (!bounds.isEmpty() && boundsChanged) {
        // Check if we should auto-zoom
        if (autoZoom) {
            const center = bounds.getCenter();

            // Set the center of the map to the polygon's center
            map.setCenter(center);

            // Adjust the viewport to contain the polygon
            map.fitBounds(bounds);

            if (window.mapDebugMode) {
                console.log("Map bounds updated with auto-zoom");
            }
        } else if (window.mapDebugMode) {
            console.log("Map bounds calculated but auto-zoom disabled");
        }
    }

    // Note: We no longer call updateMarkerCluster here
    // It's now called directly from updateMarker when needed
}

// Function to create a custom renderer for the marker clusterer
function createCustomRenderer() {
    // Return cached renderer if available
    if (cachedCustomRenderer) {
        if (window.mapDebugMode) {
            console.log("Using cached custom renderer");
        }
        return cachedCustomRenderer;
    }

    // Create a new renderer and cache it
    cachedCustomRenderer = {
        render: ({ count, position, markers: clusterMarkers }) => {
            // Only log in debug mode to improve performance
            if (window.mapDebugMode) {
                console.log(`Rendering cluster at position ${position.lat}, ${position.lng} with count: ${count}`);
                console.log(`Cluster markers array length: ${clusterMarkers ? clusterMarkers.length : 'undefined'}`);
            }

            // Ensure count is a valid number
            if (!count || isNaN(count) || count < 1) {
                // If we have cluster markers, use their length instead
                if (clusterMarkers && clusterMarkers.length > 0) {
                    count = clusterMarkers.length;
                    console.log(`Corrected invalid cluster count to ${count} based on markers array length`);
                } else {
                    console.warn(`Invalid cluster count: ${count}, using default value of 1`);
                    count = 1;
                }
            }
            // Determine the cluster status based on the devices it contains
            let hasWarning = false;
            let hasDanger = false;
            let hasOffline = false;
            let hasMaintenance = false;

            // Check if we have markers in the cluster
            if (clusterMarkers && clusterMarkers.length > 0) {
                if (window.mapDebugMode) {
                    console.log(`Processing ${clusterMarkers.length} markers in cluster`);
                }

                // Get the device IDs from the markers in this cluster
                // Use a more efficient approach to check statuses

                // First, check if we have any markers with cached status info
                let statusChecked = false;

                // Process in batches for better performance with large clusters
                const batchSize = 50;
                const totalMarkers = clusterMarkers.length;

                // Function to process a batch of markers
                function processMarkerBatch(startIndex) {
                    const endIndex = Math.min(startIndex + batchSize, totalMarkers);

                    for (let i = startIndex; i < endIndex; i++) {
                        const marker = clusterMarkers[i];

                        // Skip logging for performance unless in debug mode
                        const shouldLog = window.mapDebugMode && (i % 50 === 0); // Log less frequently

                        // Each marker in the cluster has a reference to the original marker
                        if (marker.originalMarker) {
                            // Check if the marker has a cached device ID
                            let deviceId = marker._cachedDeviceId;

                            // If no cached ID, find it
                            if (!deviceId) {
                                // Store a reference to the original marker for faster lookup
                                const originalMarker = marker.originalMarker;

                                // Use a more efficient way to find the device ID
                                for (const id in markers) {
                                    if (markers[id] === originalMarker) {
                                        deviceId = id;
                                        // Cache the device ID for future use
                                        marker._cachedDeviceId = deviceId;
                                        break;
                                    }
                                }
                            }

                            if (deviceId && deviceStatuses[deviceId]) {
                                statusChecked = true;
                                const deviceStatus = deviceStatuses[deviceId];
                                if (deviceStatus.maintenance) {
                                    hasMaintenance = true;
                                    if (shouldLog) console.log(`Marker ${deviceId} is in maintenance`);
                                } else if (deviceStatus.status === "Warning") {
                                    hasWarning = true;
                                    if (shouldLog) console.log(`Marker ${deviceId} has warning status`);
                                } else if (deviceStatus.status === "Offline") {
                                    hasOffline = true;
                                    if (shouldLog) console.log(`Marker ${deviceId} is offline`);
                                } else if (deviceStatus.status === "Danger" || deviceStatus.status === "Error") {
                                    hasDanger = true;
                                    if (shouldLog) console.log(`Marker ${deviceId} has danger/error status`);
                                }

                                // If we found any critical status, we can potentially break early
                                // This optimization helps when processing large clusters
                                if (hasDanger && hasWarning && hasOffline && hasMaintenance && !window.mapDebugMode) {
                                    return true; // We have all status types, no need to check more markers
                                }
                            }
                        }
                    }

                    // If we haven't processed all markers and haven't found all statuses,
                    // continue with the next batch
                    if (endIndex < totalMarkers && !(hasDanger && hasWarning && hasOffline && hasMaintenance)) {
                        return processMarkerBatch(endIndex);
                    }

                    return false;
                }

                // Start processing the first batch
                const earlyTermination = processMarkerBatch(0);

                if (earlyTermination && window.mapDebugMode) {
                    console.log("Early termination of status checking - all statuses found");
                }

                // If we didn't check any status (no markers had valid device IDs),
                // use a default status
                if (!statusChecked) {
                    if (window.mapDebugMode) {
                        console.log("No status checked for this cluster, using default (normal)");
                    }
                }
            } else {
                // Fallback to the old method if no markers are provided
                // This should rarely happen with the new MarkerClusterer implementation
                if (window.mapDebugMode) {
                    console.log("No markers in cluster array, using fallback method to determine cluster markers");
                }

                // Get the marker IDs in this cluster - use a more efficient approach
                const maxDistance = clusterRadius * 1000; // Convert km to meters
                const markersInCluster = [];

                // Create position LatLng object once for better performance
                const clusterPosition = new google.maps.LatLng(position.lat, position.lng);

                // Use a for...in loop for better performance with early termination
                for (const id in markers) {
                    const marker = markers[id];

                    // Only consider markers that are visible on the map
                    if (!marker.map) continue;

                    // Skip markers with invalid positions
                    if (!marker.position || typeof marker.position.lat === 'undefined' ||
                        typeof marker.position.lng === 'undefined') {
                        continue;
                    }

                    // Check if this marker is within the cluster radius
                    const markerPosition = new google.maps.LatLng(
                        marker.position.lat,
                        marker.position.lng
                    );

                    const distance = google.maps.geometry.spherical.computeDistanceBetween(
                        markerPosition,
                        clusterPosition
                    );

                    // Check if within radius
                    if (distance <= maxDistance) {
                        markersInCluster.push(id);

                        // Log for debugging (only in debug mode and only for a few markers)
                        if (window.mapDebugMode && parseInt(id) % 100 === 0) {
                            console.log(`Marker ${id} distance: ${distance.toFixed(2)}m, max distance: ${maxDistance.toFixed(2)}m, within radius: true`);
                        }
                    }
                }

                // Log the number of markers in this cluster (only in debug mode)
                if (window.mapDebugMode) {
                    console.log(`Fallback method found ${markersInCluster.length} markers in cluster at position ${position.lat}, ${position.lng}`);
                }

                // Check the status of each device in the cluster
                markersInCluster.forEach(id => {
                    if (deviceStatuses[id]) {
                        const deviceStatus = deviceStatuses[id];
                        if (deviceStatus.maintenance) {
                            hasMaintenance = true;
                        } else if (deviceStatus.status === "Warning") {
                            hasWarning = true;
                        } else if (deviceStatus.status === "Offline") {
                            hasOffline = true;
                        } else if (deviceStatus.status === "Danger" || deviceStatus.status === "Error") {
                            hasDanger = true;
                        }
                    }
                });
            }

            // Check if we should use custom icons or colored circles
            const useCustomIcons = document.getElementById('use-custom-icons') &&
                                  document.getElementById('use-custom-icons').checked;

            if (useCustomIcons) {
                // Determine which icon to use based on the status
                let iconType = "normal"; // Default
                if (hasDanger || hasOffline) {
                    iconType = "danger";
                } else if (hasWarning) {
                    iconType = "warning";
                } else if (hasMaintenance) {
                    iconType = "maintenance";
                }

                // Create a custom HTML element for the cluster icon
                const div = document.createElement("div");
                div.style.width = "60px";
                div.style.height = "60px";
                div.style.position = "relative";
                div.style.display = "flex";
                div.style.justifyContent = "center";
                div.style.alignItems = "center";

                // Create a colored circle based on status
                let bgColor;
                if (iconType === "danger") {
                    bgColor = "#DB4437"; // Red
                } else if (iconType === "warning" || iconType === "maintenance") {
                    bgColor = "#F4B400"; // Yellow
                } else {
                    bgColor = "#4285F4"; // Blue
                }

                // Create circle element
                const circleElement = document.createElement('div');
                circleElement.style.width = '50px';
                circleElement.style.height = '50px';
                circleElement.style.borderRadius = '50%';
                circleElement.style.backgroundColor = bgColor;
                circleElement.style.position = 'absolute';
                circleElement.style.top = '50%';
                circleElement.style.left = '50%';
                circleElement.style.transform = 'translate(-50%, -50%)';

                // Add status indicator
                const statusIndicator = document.createElement('div');
                statusIndicator.style.position = 'absolute';
                statusIndicator.style.top = '15%';
                statusIndicator.style.right = '15%';
                statusIndicator.style.width = '15px';
                statusIndicator.style.height = '15px';
                statusIndicator.style.borderRadius = '50%';

                if (iconType === "danger") {
                    statusIndicator.style.backgroundColor = "#DB4437"; // Red
                    statusIndicator.innerHTML = "!";
                } else if (iconType === "warning") {
                    statusIndicator.style.backgroundColor = "#F4B400"; // Yellow
                    statusIndicator.innerHTML = "!";
                } else if (iconType === "maintenance") {
                    statusIndicator.style.backgroundColor = "#F4B400"; // Yellow
                    statusIndicator.innerHTML = "🔧";
                }

                div.appendChild(circleElement);
                div.appendChild(statusIndicator);

                // Add count text
                const countDiv = document.createElement("div");
                countDiv.style.color = "white";
                countDiv.style.fontWeight = "bold";
                countDiv.style.fontSize = "18px"; // Increased font size
                countDiv.style.textAlign = "center";
                countDiv.style.textShadow = "1px 1px 2px rgba(0,0,0,0.9)"; // Stronger shadow
                countDiv.style.zIndex = "1000"; // Higher z-index to ensure visibility
                countDiv.style.position = "relative"; // Ensure proper stacking
                countDiv.style.pointerEvents = "none"; // Allow clicks to pass through
                countDiv.innerText = String(count);
                div.appendChild(countDiv);

                // Log the count for debugging (only in debug mode)
                if (window.mapDebugMode) {
                    console.log(`Creating cluster with count: ${count}`);
                }

                // Create an advanced marker with the custom HTML
                const marker = new google.maps.marker.AdvancedMarkerElement({
                    position,
                    content: div,
                    zIndex: 1000 + count,
                });

                // Make sure the count is visible
                // Sometimes the z-index or other styling issues can hide the count
                setTimeout(() => {
                    if (countDiv && countDiv.parentNode) {
                        // Ensure the count is on top and visible
                        countDiv.style.zIndex = "1000";
                        countDiv.style.fontSize = "16px";
                        countDiv.style.fontWeight = "bold";
                    }
                }, 10);

                return marker;
            } else {
                // Use colored circles (original implementation)
                // Determine the color based on the status
                let color = "#4285F4"; // Default blue for normal status
                if (hasDanger || hasOffline) {
                    color = "#DB4437"; // Red for danger/offline
                } else if (hasWarning || hasMaintenance) {
                    color = "#F4B400"; // Yellow for warning/maintenance
                }

                // Create a div for the cluster icon instead of using SVG
                // This avoids the will-change memory consumption warning
                const circleElement = document.createElement('div');
                circleElement.style.width = '50px';
                circleElement.style.height = '50px';
                circleElement.style.position = 'relative';

                // Create three concentric circles with CSS
                const circle1 = document.createElement('div');
                circle1.style.position = 'absolute';
                circle1.style.top = '50%';
                circle1.style.left = '50%';
                circle1.style.transform = 'translate(-50%, -50%)';
                circle1.style.width = '44px';
                circle1.style.height = '44px';
                circle1.style.borderRadius = '50%';
                circle1.style.backgroundColor = color;
                circle1.style.opacity = '0.8';

                const circle2 = document.createElement('div');
                circle2.style.position = 'absolute';
                circle2.style.top = '50%';
                circle2.style.left = '50%';
                circle2.style.transform = 'translate(-50%, -50%)';
                circle2.style.width = '36px';
                circle2.style.height = '36px';
                circle2.style.borderRadius = '50%';
                circle2.style.backgroundColor = color;
                circle2.style.opacity = '0.3';

                const circle3 = document.createElement('div');
                circle3.style.position = 'absolute';
                circle3.style.top = '50%';
                circle3.style.left = '50%';
                circle3.style.transform = 'translate(-50%, -50%)';
                circle3.style.width = '28px';
                circle3.style.height = '28px';
                circle3.style.borderRadius = '50%';
                circle3.style.backgroundColor = color;
                circle3.style.opacity = '0.2';

                // Add circles to the container
                circleElement.appendChild(circle1);
                circleElement.appendChild(circle2);
                circleElement.appendChild(circle3);

                // Add count text overlay
                const countDiv = document.createElement('div');
                countDiv.style.position = 'absolute';
                countDiv.style.top = '50%';
                countDiv.style.left = '50%';
                countDiv.style.transform = 'translate(-50%, -50%)';
                countDiv.style.color = 'white';
                countDiv.style.fontSize = '18px'; // Increased font size
                countDiv.style.fontWeight = 'bold';
                countDiv.style.textShadow = '1px 1px 2px rgba(0,0,0,0.9)'; // Stronger shadow
                countDiv.style.zIndex = '1000'; // Higher z-index to ensure visibility
                countDiv.style.pointerEvents = 'none'; // Allow clicks to pass through
                countDiv.innerText = String(count);

                // Add a background to make the text more visible
                countDiv.style.backgroundColor = 'rgba(0,0,0,0.3)';
                countDiv.style.borderRadius = '50%';
                countDiv.style.padding = '2px 6px';

                // Create container for the marker
                const container = document.createElement('div');
                container.style.position = 'relative';
                container.appendChild(circleElement);
                container.appendChild(countDiv);

                // Log the count for debugging (only in debug mode)
                if (window.mapDebugMode) {
                    console.log(`Creating circle cluster with count: ${count}`);
                }

                // Make sure the count is visible and properly styled
                countDiv.style.zIndex = "1000";
                countDiv.style.fontSize = "16px";
                countDiv.style.fontWeight = "bold";
                countDiv.style.textShadow = "1px 1px 2px rgba(0,0,0,0.7)";

                // Create the advanced marker
                const marker = new google.maps.marker.AdvancedMarkerElement({
                    position,
                    content: container,
                    zIndex: 1000 + count
                });

                // Make sure the count is visible after a short delay
                setTimeout(() => {
                    if (countDiv && countDiv.parentNode) {
                        // Ensure the count is on top and visible
                        countDiv.style.zIndex = "1000";
                    }
                }, 10);

                return marker;
            }
        }
    };

    return cachedCustomRenderer;
}

// Function to update the marker cluster with the current markers
function updateMarkerCluster() {
    // Make sure the map is initialized
    if (!mapInitialized || !map) {
        return;
    }

    // Throttle updates for better performance
    if (clusterUpdatePending) {
        // If an update is already pending, clear the timeout and set a new one
        clearTimeout(clusterUpdateTimeout);
    }

    // Set a flag to indicate an update is pending
    clusterUpdatePending = true;

    // Schedule the update after a short delay to batch multiple updates together
    clusterUpdateTimeout = setTimeout(() => {
        // Reset the pending flag
        clusterUpdatePending = false;

        // Perform the actual update
        performMarkerClusterUpdate();
    }, CLUSTER_UPDATE_THROTTLE_MS);
}

// The actual implementation of marker cluster update
function performMarkerClusterUpdate() {
    // Make sure the map is initialized
    if (!mapInitialized || !map) {
        return;
    }

    // Check if we have any markers
    const advancedMarkers = Object.values(markers);
    if (advancedMarkers.length === 0) {
        return;
    }

    // Log the number of markers for debugging (only in debug mode)
    if (window.mapDebugMode) {
        console.log(`Processing ${advancedMarkers.length} markers with radius ${clusterRadius} km (${clusterRadius * 1000} meters)`);
    }

    try {
        // The MarkerClusterer library is loaded via CDN and should be available in the global scope
        const clustererLib = window.markerClusterer;

        if (!clustererLib) {
            console.warn("MarkerClusterer library not loaded, using standard markers");
            // Ensure all markers are visible on the map
            advancedMarkers.forEach(marker => {
                if (!marker.map) {
                    marker.map = map;
                }
            });
            return;
        }

        // Remove existing cluster if it exists
        if (markerCluster) {
            try {
                markerCluster.setMap(null);
                markerCluster = null;
            } catch (e) {
                console.warn("Error removing existing marker cluster:", e);
            }
        }

        // Get the current visibility threshold (may have been updated via UI)
        const visibilityThreshold = window.MARKER_VISIBILITY_ZOOM || MARKER_VISIBILITY_ZOOM;

        // Set marker visibility based on zoom level
        const showMarkers = currentZoom >= visibilityThreshold;
        if (showMarkers) {
            // Show all markers if zoom level is high enough
            advancedMarkers.forEach(marker => {
                marker.map = map;
            });
        } else {
            // Hide all markers if zoom level is too low
            advancedMarkers.forEach(marker => {
                marker.map = null;
            });
        }

        // Create a custom adapter for AdvancedMarkerElement to work with MarkerClusterer
        // Instead of creating actual Marker instances (which are deprecated),
        // we'll create lightweight objects with just the methods needed for clustering
        const compatibleMarkers = advancedMarkers.filter(marker => {
            // Filter out markers with invalid positions
            if (!marker || !marker.position ||
                typeof marker.position.lat === 'undefined' ||
                typeof marker.position.lng === 'undefined') {
                if (window.mapDebugMode) {
                    console.warn("Skipping marker with invalid position:", marker);
                }
                return false;
            }
            return true;
        }).map(advancedMarker => {
            // Check if we have a cached adapter for this marker
            const markerId = Object.keys(markers).find(id => markers[id] === advancedMarker);

            if (markerId && markerAdapterCache[markerId]) {
                // Update the cached adapter's position if it has changed
                const cachedAdapter = markerAdapterCache[markerId];

                // Check if position has changed
                if (cachedAdapter.originalMarker.position.lat !== advancedMarker.position.lat ||
                    cachedAdapter.originalMarker.position.lng !== advancedMarker.position.lng) {

                    // Update the position in the cached adapter
                    cachedAdapter._position = new google.maps.LatLng(
                        advancedMarker.position.lat,
                        advancedMarker.position.lng
                    );

                    // Update the original marker reference
                    cachedAdapter.originalMarker = advancedMarker;
                }

                return cachedAdapter;
            }

            // Create a simple object with the necessary methods for MarkerClusterer
            const markerAdapter = {
                // Store a reference to the original marker
                originalMarker: advancedMarker,

                // Cache the LatLng object for better performance
                _position: new google.maps.LatLng(
                    advancedMarker.position.lat,
                    advancedMarker.position.lng
                ),

                // Implement getPosition() method required by MarkerClusterer
                getPosition: function() {
                    try {
                        // Return the cached position if available
                        if (this._position) {
                            return this._position;
                        }

                        // Ensure position has valid lat/lng values
                        if (!advancedMarker.position ||
                            typeof advancedMarker.position.lat === 'undefined' ||
                            typeof advancedMarker.position.lng === 'undefined') {
                            if (window.mapDebugMode) {
                                console.warn("Invalid marker position in getPosition():", advancedMarker.position);
                            }
                            // Return a default position to prevent errors
                            return new google.maps.LatLng(0, 0);
                        }

                        // Create and cache the position
                        this._position = new google.maps.LatLng(
                            advancedMarker.position.lat,
                            advancedMarker.position.lng
                        );

                        return this._position;
                    } catch (e) {
                        if (window.mapDebugMode) {
                            console.error("Error in getPosition():", e);
                        }
                        // Return a default position to prevent errors
                        return new google.maps.LatLng(0, 0);
                    }
                },

                // Add getVisible method required by MarkerClusterer
                getVisible: function() {
                    // Only include markers that are visible on the map
                    return advancedMarker.map !== null;
                },

                // Add any other methods that might be needed by MarkerClusterer
                setMap: function(map) {
                    // This is called by MarkerClusterer when adding/removing from clusters
                    // We need to update the original marker's map property
                    if (advancedMarker.map !== map) {
                        advancedMarker.map = map;
                    }
                },

                // Add a position property that MarkerClusterer might use
                position: advancedMarker.position,

                // Add visible property - sync with the original marker
                get visible() {
                    return advancedMarker.map !== null;
                },

                // Add a map property - sync with the original marker
                get map() {
                    return advancedMarker.map;
                },

                set map(value) {
                    advancedMarker.map = value;
                },

                // Add a getMap method
                getMap: function() {
                    // Return the map when the marker should be visible
                    // This is important for the clustering algorithm to work properly
                    return advancedMarker.map;
                }
            };

            // Store in cache if we have a marker ID
            if (markerId) {
                markerAdapterCache[markerId] = markerAdapter;
            }

            return markerAdapter;
        });

        // Use cached algorithm if available and radius hasn't changed
        let algorithm;

        if (cachedClusterAlgorithm && cachedClusterAlgorithm.clusterRadius === clusterRadius) {
            algorithm = cachedClusterAlgorithm.algorithm;

            if (window.mapDebugMode) {
                console.log("Using cached cluster algorithm");
            }
        } else {
            // Convert kilometers to meters for maxDistance
            const maxDistanceMeters = clusterRadius * 1000;

            // Create a new GridAlgorithm with the specified maxDistance
            // The gridSize is also important for clustering - smaller values create more clusters
            // We'll adjust it based on the cluster radius to maintain proportionality
            const gridSize = Math.max(20, Math.min(100, Math.round(60 * (clusterRadius / 3))));

            algorithm = new clustererLib.GridAlgorithm({
                maxDistance: maxDistanceMeters,
                gridSize: gridSize,
                maxZoom: 15
            });

            // Cache the algorithm for future use
            cachedClusterAlgorithm = {
                algorithm: algorithm,
                clusterRadius: clusterRadius
            };

            // Log the algorithm configuration for debugging (only in debug mode)
            if (window.mapDebugMode) {
                console.log(`GridAlgorithm created with maxDistance: ${maxDistanceMeters} meters (${clusterRadius} km), gridSize: ${gridSize}`);
            }
        }


        // Create a custom renderer for the clusters
        const renderer = createCustomRenderer();

        // Create a new MarkerClusterer with the custom renderer
        try {
            // Log the markers that will be used for clustering (only in debug mode)
            if (window.mapDebugMode) {
                console.log(`Creating MarkerClusterer with ${compatibleMarkers.length} markers`);
                console.log(`Visible markers: ${compatibleMarkers.filter(m => m.getVisible()).length}`);
            }

            // Filter out any markers that might cause issues
            const validMarkers = compatibleMarkers.filter(marker => {
                // Check if the marker has a valid position
                if (!marker.getPosition || typeof marker.getPosition !== 'function') {
                    console.warn("Marker missing getPosition method, skipping");
                    return false;
                }

                const position = marker.getPosition();
                if (!position || !position.lat || !position.lng) {
                    console.warn("Marker has invalid position, skipping");
                    return false;
                }

                return true;
            });

            if (window.mapDebugMode) {
                console.log(`Filtered markers: ${validMarkers.length} valid out of ${compatibleMarkers.length} total`);
            }

            // Only proceed if we have valid markers
            if (validMarkers.length === 0) {
                console.warn("No valid markers for clustering, skipping cluster creation");
                return;
            }

            // Create the MarkerClusterer with the specified algorithm and renderer
            markerCluster = new clustererLib.MarkerClusterer({
                map: map,
                markers: validMarkers,
                algorithm: algorithm,
                renderer: renderer,
                onClusterClick: function(_, cluster) {
                    // First parameter is the event, which we don't need (using _ as convention for unused params)
                    // Handle cluster click - zoom in to expand the cluster
                    const bounds = new google.maps.LatLngBounds();

                    // Check if cluster has markers before trying to iterate
                    if (cluster.markers && cluster.markers.length > 0) {
                        cluster.markers.forEach(marker => {
                            if (marker && marker.getPosition) {
                                bounds.extend(marker.getPosition());
                            }
                        });

                        // Only fit bounds if we have valid markers
                        if (!bounds.isEmpty()) {
                            map.fitBounds(bounds);
                        }
                    }

                    // Log the cluster details for debugging
                    console.log(`Cluster clicked with ${cluster.markers ? cluster.markers.length : 0} markers`);
                    if (cluster.position) {
                        console.log(`Cluster center: ${cluster.position.lat()}, ${cluster.position.lng()}`);
                    }

                    // Log the first few markers in this cluster
                    if (cluster.markers && cluster.markers.length > 0) {
                        console.log("Sample markers in this cluster:");
                        cluster.markers.slice(0, 3).forEach((marker, i) => {
                            if (marker && marker.getPosition) {
                                console.log(`Marker ${i}: position=${marker.getPosition().lat()},${marker.getPosition().lng()}`);
                            } else {
                                console.log(`Marker ${i}: invalid or missing getPosition method`);
                            }
                        });
                    }
                }
            });

            // Store the current cluster radius on the markerCluster object for reference
            markerCluster.clusterRadius = clusterRadius;

            // Force the algorithm to recalculate clusters
            if (typeof markerCluster.render === 'function') {
                markerCluster.render();
            }

            console.log("MarkerClusterer created successfully");
        } catch (error) {
            console.error("Error creating MarkerClusterer:", error);
            console.error(error.stack);

            // Get the current visibility threshold (may have been updated via UI)
            const visibilityThreshold = window.MARKER_VISIBILITY_ZOOM || MARKER_VISIBILITY_ZOOM;

            // Fallback: set marker visibility based on zoom level
            const showMarkers = currentZoom >= visibilityThreshold;
            advancedMarkers.forEach(marker => {
                marker.map = showMarkers ? map : null;
            });
        }

        // Note: We've already made all markers visible earlier in the function
        // No need to set visibility again here

        console.log(`Marker cluster created with ${compatibleMarkers.length} markers and radius ${clusterRadius} km`);
    } catch (error) {
        console.error("Error creating marker cluster:", error);
        console.error(error.stack);

        // Get the current visibility threshold (may have been updated via UI)
        const visibilityThreshold = window.MARKER_VISIBILITY_ZOOM || MARKER_VISIBILITY_ZOOM;

        // Fallback: set marker visibility based on zoom level
        const showMarkers = currentZoom >= visibilityThreshold;
        advancedMarkers.forEach(marker => {
            marker.map = showMarkers ? map : null;
        });
    }
}

// Throttling variables for marker visibility updates
let visibilityUpdatePending = false;
let visibilityUpdateTimeout = null;
const VISIBILITY_UPDATE_THROTTLE_MS = 200; // Throttle visibility updates to once per 200ms

// Function to update marker visibility based on zoom level
function updateMarkerVisibility() {
    // Throttle updates for better performance
    if (visibilityUpdatePending) {
        // If an update is already pending, clear the timeout and set a new one
        clearTimeout(visibilityUpdateTimeout);
    }

    // Set a flag to indicate an update is pending
    visibilityUpdatePending = true;

    // Schedule the update after a short delay to batch multiple updates together
    visibilityUpdateTimeout = setTimeout(() => {
        // Reset the pending flag
        visibilityUpdatePending = false;

        // Perform the actual update
        performMarkerVisibilityUpdate();
    }, VISIBILITY_UPDATE_THROTTLE_MS);
}

// The actual implementation of marker visibility update
function performMarkerVisibilityUpdate() {
    // Get the current visibility threshold (may have been updated via UI)
    const visibilityThreshold = window.MARKER_VISIBILITY_ZOOM || MARKER_VISIBILITY_ZOOM;

    const showMarkers = currentZoom >= visibilityThreshold;

    // Use a more efficient approach to update visibility
    // Process markers in batches to avoid blocking the UI thread
    const markerIds = Object.keys(markers);
    const batchSize = 100; // Process 100 markers at a time

    // Function to process a batch of markers
    function processBatch(startIndex) {
        const endIndex = Math.min(startIndex + batchSize, markerIds.length);

        // Process this batch
        for (let i = startIndex; i < endIndex; i++) {
            const id = markerIds[i];
            const marker = markers[id];

            // Only update if the marker has a map property (is an AdvancedMarkerElement)
            if (marker) {
                if (showMarkers) {
                    // Show marker if zoom level is high enough
                    marker.map = map;
                } else {
                    // Hide marker if zoom level is too low
                    marker.map = null;
                }
            }
        }

        // If there are more markers to process, schedule the next batch
        if (endIndex < markerIds.length) {
            setTimeout(() => {
                processBatch(endIndex);
            }, 0);
        } else {
            // All batches processed, update clusters if needed
            // If markers are hidden, make sure clusters are visible
            if (!showMarkers && markerCluster) {
                // Force update of the marker cluster
                updateMarkerCluster();
            }

            // Log completion
            if (window.mapDebugMode) {
                console.log(`Marker visibility updated: ${showMarkers ? 'showing' : 'hiding'} individual markers at zoom level ${currentZoom} (threshold: ${visibilityThreshold})`);
            }
        }
    }

    // Start processing the first batch
    processBatch(0);
}

function setFocus(markerId) {
    // close all open info windows
    for (const id in markers) {
        if (infoWindows[id]) {
            infoWindows[id].close(map, markers[id]);
        }
    }

    // Zoom in on the marker and open its info window
    map.setZoom(18);
    map.setCenter(markers[markerId].position);

    // Make sure the marker is visible regardless of zoom level
    markers[markerId].map = map;

    infoWindows[markerId].open(map, markers[markerId]);
}
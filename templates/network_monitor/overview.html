{% extends "horizontal_base.html" %}
{% load static %}
{% block title %}
  Devices
{% endblock title %}
{% block extra_css %}
<style>
  /* Custom styles for the floating controls panel */
  #map-controls-panel {
    max-height: 80vh;
    overflow-y: auto;
  }

  #map-controls-panel .card {
    border: none;
    box-shadow: none;
  }

  #controls-toggle-btn {
    transition: all 0.3s ease;
  }

  #controls-toggle-btn:hover {
    transform: translateY(-50%) scale(1.1);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    #map-controls-panel {
      width: 280px;
      right: -280px !important;
    }

    #controls-toggle-btn {
      width: 45px;
      height: 45px;
      right: 15px !important;
    }
  }

  /* Smooth scrollbar for the panel */
  #map-controls-panel::-webkit-scrollbar {
    width: 6px;
  }

  #map-controls-panel::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  #map-controls-panel::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  #map-controls-panel::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
</style>
{% endblock extra_css %}
{% block content %}
  <!-- Start Content-->
  <div id="network-overview" class="container-fluid">
    <!-- start page title -->
    <div class="row">
      <div class="col-lg">
        <div class="page-title-box">
          <div class="col-lg">
            <div class="page-title-box d-flex align-items-center">
              <h4 class="page-title">Field</h4>
              <div class="form-group mb-0 ms-3">
                <div class="input-group">
                  <select class="form-select" id="field-selection">
                    <option value="0" {% if selected_field_id == "0" %}selected{% endif %}>All</option>
                    {% for field in list %}<option value="{{ field.id }}">{{ field.name }}</option>{% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- end page title -->
    <!-- Recent Acitivty, Asset List, & Network Map -->
    <div class="row">
      <!-- Recent Acitivty & Asset List -->
      <div class="col-xl-3">
        <!-- Asset List -->
        <div class="card" style="height: 35vh;">
          <div class="card-body border-top-0 border-start-0 border-end-0 border-dashed border"
               style="max-height: 0px">
            <h3 class="header-title">Assets</h3>
          </div>
          <div class="card-body pt-0" data-simplebar style="height: 28vh">
            <div id="asset-list-container"></div>
          </div>
        </div>
        <!-- Recent Activity -->
        <div class="card" style="height: 35vh;">
          <div class="card-body" style="max-height: 0px">
            <h3 class="header-title">
              Recent Activity <span id="ws-connection" class="badge">Disconnected</span>
            </h3>
          </div>
          <div class="card-header bg-light-lighten border-top border-bottom border-light py-1 text-center">
            <div class="d-flex align-items-center justify-content-center">
              <i class="mdi mdi-access-point mdi-18px px-1"></i>
              <p class="m-0">
                Last update <b id="last-update">—</b> ago
              </p>
            </div>
          </div>
          <div class="card-body pt-2" data-simplebar style="height: 22vh">
            <div class="timeline-alt py-0">
              <div id="recent-activity-container"></div>
            </div>
          </div>
        </div>
      </div>
      <!-- Network Map -->
      <div class="col-xl-9">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h3 class="header-title">Map</h3>
              <div class="d-flex align-items-center">
                <div class="form-check form-switch me-3">
                  <input class="form-check-input" type="checkbox" id="auto-zoom-toggle">
                  <label class="form-check-label" for="auto-zoom-toggle">Auto-zoom on updates</label>
                </div>
                {% if settings.debug == "True" %}
                <div class="form-check form-switch me-3">
                  <input class="form-check-input" type="checkbox" id="debug-mode-toggle">
                  <label class="form-check-label" for="debug-mode-toggle">Debug mode</label>
                </div>
                {% endif %}
              </div>
            </div>
            <div id="device_map" class="gmaps" style="height: 64.5vh"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Controls Panel -->
    <div id="map-controls-panel" class="position-fixed" style="
      top: 50%;
      right: -300px;
      transform: translateY(-50%);
      width: 300px;
      background: white;
      border-radius: 8px 0 0 8px;
      box-shadow: -2px 0 10px rgba(0,0,0,0.1);
      z-index: 1000;
      transition: right 0.3s ease;
    ">
      <div class="card mb-0">
        <div class="card-header bg-primary text-white">
          <h5 class="card-title mb-0">
            <i class="mdi mdi-cog me-2"></i>Map Controls
          </h5>
        </div>
        <div class="card-body">
          <!-- Cluster Radius Control -->
          <div class="mb-3">
            <label for="cluster-radius" class="form-label">
              <i class="mdi mdi-group me-1"></i>Cluster Radius
            </label>
            <div class="input-group">
              <input type="range" class="form-control" id="cluster-radius" value="3" min="0.1" max="10" step="0.1">
              <span class="input-group-text"><span id="radius-value-display">3</span> km</span>
            </div>
          </div>

          <!-- Zoom Level Control -->
          <div class="mb-3">
            <label class="form-label">
              <i class="mdi mdi-magnify me-1"></i>Zoom Level
            </label>
            <div class="input-group mb-2">
              <span class="input-group-text">Current:</span>
              <span class="input-group-text"><span id="current-zoom-display">8</span></span>
            </div>
            <div class="input-group">
              <span class="input-group-text">Show Markers at Zoom ≥</span>
              <select class="form-select" id="marker-visibility-zoom">
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="13" selected>13</option>
                <option value="14">14</option>
                <option value="15">15</option>
              </select>
            </div>
          </div>

          <!-- Additional Controls -->
          <div class="mb-0">
            <div class="form-check form-switch mb-2">
              <input class="form-check-input" type="checkbox" id="auto-zoom-toggle-panel">
              <label class="form-check-label" for="auto-zoom-toggle-panel">
                <i class="mdi mdi-crosshairs-gps me-1"></i>Auto-zoom on updates
              </label>
            </div>
            {% if settings.debug == "True" %}
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="debug-mode-toggle-panel">
              <label class="form-check-label" for="debug-mode-toggle-panel">
                <i class="mdi mdi-bug me-1"></i>Debug mode
              </label>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Toggle Button -->
    <button id="controls-toggle-btn" class="btn btn-primary position-fixed" style="
      top: 50%;
      right: 20px;
      transform: translateY(-50%);
      z-index: 1001;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    " title="Toggle Map Controls">
      <i class="mdi mdi-cog mdi-18px"></i>
    </button>

  </div>
{% endblock content %}
{% block extra_javascript %}
  <!-- Third party js -->
  <script src="https://unpkg.com/@googlemaps/markerclusterer@2.0.15/dist/index.min.js"></script>
  <script src="{% static 'js/custom/dashboards/common/device_map.js' %}"></script>
  <script src="https://maps.googleapis.com/maps/api/js?key={{ GOOGLE_MAPS_API_KEY }}&libraries=geometry&callback=initMap"
          defer></script>
  <!-- App js -->
  <script>
    // load field ids
    const fieldIds = {{ selected_field_id }} ? [{{ selected_field_id }}] : {{ field_ids }};
    const selectedFieldId = {{ selected_field_id }};
    // default cluster radius in kilometers
    const defaultClusterRadius = 3;

    // Controls panel toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
      const controlsPanel = document.getElementById('map-controls-panel');
      const toggleBtn = document.getElementById('controls-toggle-btn');
      const toggleIcon = toggleBtn.querySelector('i');
      let isPanelOpen = false;

      // Toggle panel visibility
      function togglePanel() {
        isPanelOpen = !isPanelOpen;
        if (isPanelOpen) {
          controlsPanel.style.right = '0px';
          toggleBtn.style.right = '320px';
          toggleIcon.className = 'mdi mdi-close mdi-18px';
          toggleBtn.title = 'Close Map Controls';
        } else {
          controlsPanel.style.right = '-300px';
          toggleBtn.style.right = '20px';
          toggleIcon.className = 'mdi mdi-cog mdi-18px';
          toggleBtn.title = 'Open Map Controls';
        }
      }

      // Add click event to toggle button
      toggleBtn.addEventListener('click', togglePanel);

      // Synchronize controls between original and panel
      function syncControls() {
        // Sync auto-zoom toggles
        const originalAutoZoom = document.getElementById('auto-zoom-toggle');
        const panelAutoZoom = document.getElementById('auto-zoom-toggle-panel');

        if (originalAutoZoom && panelAutoZoom) {
          panelAutoZoom.checked = originalAutoZoom.checked;

          panelAutoZoom.addEventListener('change', function() {
            originalAutoZoom.checked = this.checked;
            originalAutoZoom.dispatchEvent(new Event('change'));
          });

          originalAutoZoom.addEventListener('change', function() {
            panelAutoZoom.checked = this.checked;
          });
        }

        // Sync debug mode toggles
        const originalDebug = document.getElementById('debug-mode-toggle');
        const panelDebug = document.getElementById('debug-mode-toggle-panel');

        if (originalDebug && panelDebug) {
          panelDebug.checked = originalDebug.checked;

          panelDebug.addEventListener('change', function() {
            originalDebug.checked = this.checked;
            originalDebug.dispatchEvent(new Event('change'));
          });

          originalDebug.addEventListener('change', function() {
            panelDebug.checked = this.checked;
          });
        }
      }

      // Initialize synchronization after a short delay to ensure all elements are loaded
      setTimeout(syncControls, 100);

      // Close panel when clicking outside
      document.addEventListener('click', function(event) {
        if (isPanelOpen &&
            !controlsPanel.contains(event.target) &&
            !toggleBtn.contains(event.target)) {
          togglePanel();
        }
      });

      // Prevent panel from closing when clicking inside it
      controlsPanel.addEventListener('click', function(event) {
        event.stopPropagation();
      });
    });
  </script>
  <script src="{% static 'js/custom/select_field.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/events_list_generator.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/time_elapsed.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/overview.js' %}"></script>
  <script src="{% static 'js/custom/dashboards/common/fetch_fields.js' %}"></script>
{% endblock extra_javascript %}

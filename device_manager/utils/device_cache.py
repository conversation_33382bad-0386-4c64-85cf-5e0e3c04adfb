import json
from django.conf import settings
from device_manager.models import Device
from django_redis import get_redis_connection


class DeviceCache:
    """
    Class for handling device caching operations using Redis.
    Provides methods for storing and retrieving device data and status.
    """

    def __init__(self):
        # Get Redis connection from django-redis cache
        self.r = get_redis_connection("default")

    def set_device_stat(self, device_id, stat_value, ttl=60):
        self.r.set(f"device:{device_id}:stat", stat_value, ex=ttl)

    def get_device_stat(self, device_id):
        stat = self.r.get(f"device:{device_id}:stat")
        # Handle bytes response
        if isinstance(stat, bytes):
            stat = stat.decode('utf-8')
        return stat

    def get_device_cache_key(self, device_id):
        return f"device:{device_id}"

    def cache_device(self, device, ttl=None):
        if isinstance(device, Device):
            key = self.get_device_cache_key(device.id)
            data = device.to_dict()
        else:
            key = self.get_device_cache_key(device.get("id"))
            data = device
        # Set device data with TTL
        self.r.set(key, json.dumps(data))
        # Set device stat with TTL
        if ttl is None:
            ttl=int(data.get("offp"))*60
        self.set_device_stat(data.get("id"), data.get("stat"), ttl)

    def get_cached_device(self, device_id):
        key = self.get_device_cache_key(device_id)
        data = self.r.get(key)
        if data:
            # Handle bytes response
            if isinstance(data, bytes):
                data = data.decode('utf-8')
            return Device.from_json(data)
        return None
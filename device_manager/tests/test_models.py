from django.test import TestCase, tag
from django.utils import timezone
from unittest.mock import patch, MagicMock
import json
import redis

from device_manager.models import Device, get_device_attrs_template, get_location_template
from fields.models import Field
from device_manager.utils.device_cache import DeviceCache


@tag('model', 'mock')
class DeviceModelTestCase(TestCase):
    """Test cases for the Device model with mocked cache functions"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create a test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )
        self.device_cache = DeviceCache()

    def test_device_creation(self):
        """Test that a device can be created with the expected attributes"""
        self.assertEqual(self.device.name, "Test Device")
        self.assertEqual(self.device.desc, "Test Description")
        self.assertEqual(self.device.euid, "ABCDEF0123456789")
        self.assertEqual(self.device.stat, "Online")
        self.assertEqual(self.device.temp, 25)
        self.assertEqual(self.device.batt, 80)
        self.assertEqual(self.device.chrg, False)
        self.assertEqual(self.device.actv, True)
        self.assertEqual(self.device.mntc, False)
        self.assertEqual(self.device.hidn, False)
        self.assertEqual(self.device.attr, {"client": {"temp": 25}})
        self.assertEqual(self.device.type, "Whiskers Node V1")
        self.assertEqual(self.device.aset, "Battery")
        self.assertEqual(self.device.loca, {"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"})
        self.assertEqual(self.device.fild, self.field)
        self.assertEqual(self.device.offp, 10)

    def test_str_method(self):
        """Test the __str__ method of the Device model"""
        self.assertEqual(str(self.device), "Test Device")

    def test_to_dict_method(self):
        """Test the to_dict method of the Device model"""
        device_dict = self.device.to_dict()
        self.assertEqual(device_dict["name"], "Test Device")
        self.assertEqual(device_dict["desc"], "Test Description")
        self.assertEqual(device_dict["euid"], "ABCDEF0123456789")
        self.assertEqual(device_dict["stat"], "Online")
        self.assertEqual(device_dict["temp"], 25)
        self.assertEqual(device_dict["batt"], 80)
        self.assertEqual(device_dict["chrg"], False)
        self.assertEqual(device_dict["actv"], True)
        self.assertEqual(device_dict["mntc"], False)
        self.assertEqual(device_dict["hidn"], False)
        self.assertEqual(device_dict["attr"], {"client": {"temp": 25}})
        self.assertEqual(device_dict["type"], "Whiskers Node V1")
        self.assertEqual(device_dict["aset"], "Battery")
        self.assertEqual(device_dict["loca"], {"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"})
        self.assertEqual(device_dict["fild"], self.field.to_json())
        self.assertEqual(device_dict["offp"], 10)

    def test_to_json_method(self):
        """Test the to_json method of the Device model"""
        device_json = self.device.to_json()
        device_dict = json.loads(device_json)
        self.assertEqual(device_dict["name"], "Test Device")
        self.assertEqual(device_dict["desc"], "Test Description")
        self.assertEqual(device_dict["euid"], "ABCDEF0123456789")

    def test_from_json_method(self):
        """Test the from_json method of the Device model"""
        device_json = self.device.to_json()
        new_device = Device.from_json(device_json)
        self.assertEqual(new_device.name, "Test Device")
        self.assertEqual(new_device.desc, "Test Description")
        self.assertEqual(new_device.euid, "ABCDEF0123456789")

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_lupd_str_property(self, mock_get_cached_device):
        """Test the lupd_str property of the Device model"""
        # Mock the cached device to return None
        mock_get_cached_device.return_value = None

        # Set a specific last update time with timezone awareness
        self.device.lupd = timezone.now() - timezone.timedelta(minutes=5)
        self.device.save()

        # The exact string will depend on the time difference, but it should not be "—"
        self.assertNotEqual(self.device.lupd_str, "—")

    @patch('device_manager.utils.device_cache.DeviceCache.cache_device')
    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_save_method_with_caching(self, mock_get_cached_device, mock_cache_device):
        """Test the save method with caching functionality"""
        # Create a mock Device object to return from get_cached_device
        mock_cached_device = MagicMock(spec=Device)

        # Set up the mock device with the same attributes as self.device
        for field in Device.critical_fields:
            setattr(mock_cached_device, field, getattr(self.device, field))

        # Set the ID to match the test device
        mock_cached_device.pk = self.device.id
        mock_cached_device.id = self.device.id

        # Configure the mock to return our mock device
        mock_get_cached_device.return_value = mock_cached_device

        # Update the device
        self.device.name = "Updated Device"
        self.device.save()

        # Verify that cache_device was called
        mock_cache_device.assert_called_once_with(self.device)

        # Verify that get_cached_device was called with the correct ID
        mock_get_cached_device.assert_called_once_with(self.device.pk)


@tag('model', 'unmock')
class UnmockedDeviceModelTestCase(TestCase):
    """Test cases for the Device model with real cache interactions"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field Real",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create a test device
        self.device = Device.objects.create(
            name="Test Device Real",
            desc="Test Description for Real Tests",
            euid="REAL0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Connect to Redis using django-redis
        from django_redis import get_redis_connection

        try:
            # Get Redis connection from django-redis cache
            self.redis_client = get_redis_connection("default")
            # Test the connection
            self.redis_client.ping()
        except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError):
            # If connection fails, use a mock Redis client
            self.redis_client = MagicMock()
            self.redis_client.get.return_value = None
            self.redis_client.set.return_value = True
            self.redis_client.delete.return_value = True
            self.redis_client.ttl.return_value = 60
            print("WARNING: Could not connect to Redis, using mock Redis client instead")

    def tearDown(self):
        """Clean up after tests"""
        # Clean up Redis if it's not a mock
        if not isinstance(self.redis_client, MagicMock):
            try:
                self.redis_client.delete(f"device:{self.device.id}")
                self.redis_client.delete(f"device:{self.device.id}:stat")
            except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError):
                print("WARNING: Could not connect to Redis during tearDown")

    def test_save_method_real_caching(self):
        """Test the save method with real caching"""
        # Update the device
        self.device.name = "Updated Device Real"
        self.device.save()

        # Verify the device was cached in Redis
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        self.assertIsNotNone(cached_json)

        # Parse the JSON and verify the data
        cached_data = json.loads(cached_json)
        self.assertEqual(cached_data["name"], "Updated Device Real")

        # Verify the device was updated in the database
        updated_device = Device.objects.get(pk=self.device.id)
        self.assertEqual(updated_device.name, "Updated Device Real")

    def test_lupd_str_property_real(self):
        """Test the lupd_str property with real data"""
        # Set a specific last update time
        self.device.lupd = timezone.now() - timezone.timedelta(minutes=5)
        self.device.save()

        # The exact string will depend on the time difference, but it should not be "—"
        self.assertNotEqual(self.device.lupd_str, "—")
        # It should contain "minutes" since we set it 5 minutes ago
        self.assertIn("minute", self.device.lupd_str.lower())

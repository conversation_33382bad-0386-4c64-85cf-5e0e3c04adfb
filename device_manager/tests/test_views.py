from django.test import TestCase, Client, RequestFactory, tag
from django.urls import reverse
from django.contrib.auth.models import User
from unittest.mock import patch, MagicMock
import json
import redis

from device_manager.models import Device
from device_manager.views import (
    DeviceListView, DeviceCreateView, DeviceDetailView, DeviceUpdateView,
    delete, toggle_hide, toggle_maintenance, clear_status, clear_all_status
)
from device_manager.utils.device_cache import DeviceCache
from fields.models import Field
from accounts.models import UserProfile
from notification_center.models import Event


@tag('view', 'mock')
class DeviceViewsTestCase(TestCase):
    """Test cases for the Device views with mocked cache functions"""

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword",
            is_superuser=True
        )

        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create a test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Create a user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role="Admin",
            phon="12345678",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.profile.devs.add(self.device)

        # Set up the client and log in
        self.client = Client()
        self.client.login(username="testuser", password="testpassword")

        # Set up the request factory
        self.factory = RequestFactory()

    def test_device_list_view(self):
        """Test the DeviceListView"""
        url = reverse("device_manager:list")
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "device_manager/list.html")
        self.assertIn("list", response.context)
        self.assertEqual(len(response.context["list"]), 1)

    def test_device_detail_view(self):
        """Test the DeviceDetailView"""
        url = reverse("device_manager:detail", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertIn("device", response.context)
        self.assertEqual(response.context["device"], self.device)

    def test_device_update_view(self):
        """Test the DeviceUpdateView"""
        url = reverse("device_manager:edit", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, "device_manager/create.html")
        self.assertIn("device", response.context)
        self.assertEqual(response.context["device"], self.device)

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_toggle_hide_view(self, mock_get_cached_device):
        """Test the toggle_hide view"""
        # Mock the cached device to return None
        mock_get_cached_device.return_value = None

        url = reverse("device_manager:toggle_hide", args=[self.device.id])

        with patch('device_manager.views.get_object_or_404') as mock_get_object:
            # Configure the mock to return our device
            mock_get_object.return_value = self.device

            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect

            # Refresh the device from the database
            self.device.refresh_from_db()
            self.assertTrue(self.device.hidn)

            # Toggle again
            response = self.client.get(url)
            self.device.refresh_from_db()
            self.assertFalse(self.device.hidn)

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_toggle_maintenance_view(self, mock_get_cached_device):
        """Test the toggle_maintenance view"""
        # Mock the cached device to return None
        mock_get_cached_device.return_value = None

        url = reverse("device_manager:toggle_maintenance", args=[self.device.id])

        with patch('device_manager.views.get_object_or_404') as mock_get_object:
            # Configure the mock to return our device
            mock_get_object.return_value = self.device

            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect

            # Refresh the device from the database
            self.device.refresh_from_db()
            self.assertTrue(self.device.mntc)

            # Toggle again
            response = self.client.get(url)
            self.device.refresh_from_db()
            self.assertFalse(self.device.mntc)

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_clear_status_view(self, mock_get_cached_device):
        """Test the clear_status view"""
        # Mock the cached device to return None
        mock_get_cached_device.return_value = None

        # First set the device to a non-default state
        self.device.stat = "Warning"
        self.device.actv = True
        self.device.attr["client"]["Motion event."] = True
        self.device.attr["client"]["Shock event."] = True
        self.device.attr["client"]["Motionless event."] = False
        self.device.save()

        url = reverse("device_manager:clear_status", args=[self.device.id])

        with patch('device_manager.views.get_object_or_404') as mock_get_object:
            # Configure the mock to return our device
            mock_get_object.return_value = self.device

            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect

            # Refresh the device from the database
            self.device.refresh_from_db()
            self.assertEqual(self.device.stat, "Online")
            self.assertFalse(self.device.actv)
            self.assertFalse(self.device.attr["client"]["Motion event."])
            self.assertFalse(self.device.attr["client"]["Shock event."])
            self.assertTrue(self.device.attr["client"]["Motionless event."])

    @patch('device_manager.utils.device_cache.DeviceCache.get_cached_device')
    def test_clear_all_status_view(self, mock_get_cached_device):
        """Test the clear_all_status view"""
        # Mock the cached device to return None
        mock_get_cached_device.return_value = None

        # First set the device to a non-default state
        self.device.stat = "Warning"
        self.device.save()

        url = reverse("device_manager:clear_all_status")

        with patch('device_manager.models.Device.objects.all') as mock_all:
            # Configure the mock to return a list with our device
            mock_all.return_value = [self.device]

            response = self.client.get(url)
            self.assertEqual(response.status_code, 302)  # Redirect

            # Refresh the device from the database
            self.device.refresh_from_db()
            self.assertEqual(self.device.stat, "Online")

    def test_delete_view(self):
        """Test the delete view"""
        # Create an event for the device
        Event.objects.create(
            devi=self.device,
            type="Update",
            desc="Test event"
        )

        # Verify the event was created
        self.assertEqual(Event.objects.count(), 1)

        url = reverse("device_manager:delete", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Verify that the device and its events are deleted
        self.assertEqual(Device.objects.count(), 0)
        self.assertEqual(Event.objects.count(), 0)


@tag('view', 'unmock')
class UnmockedDeviceViewsTestCase(TestCase):
    """Test cases for the Device views with real cache interactions"""

    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.user = User.objects.create_user(
            username="testuser_real",
            email="<EMAIL>",
            password="testpassword",
            is_superuser=True
        )

        # Create a test field
        self.field = Field.objects.create(
            name="Test Field View",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create a test device
        self.device = Device.objects.create(
            name="Test Device View",
            desc="Test Description for View Tests",
            euid="VIEW0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25, "Motion event.": False, "Shock event.": False, "Motionless event.": True}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Create a user profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role="Admin",
            phon="12345678",
            titl="Test Title",
            orgn="Test Organization"
        )
        self.profile.devs.add(self.device)

        # Set up the client and log in
        self.client = Client()
        self.client.login(username="testuser_real", password="testpassword")

        # Connect to Redis using django-redis
        from django_redis import get_redis_connection

        try:
            # Get Redis connection from django-redis cache
            self.redis_client = get_redis_connection("default")
            # Test the connection
            self.redis_client.ping()
        except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError):
            # If connection fails, use a mock Redis client
            self.redis_client = MagicMock()
            self.redis_client.get.return_value = None
            self.redis_client.set.return_value = True
            self.redis_client.delete.return_value = True
            self.redis_client.ttl.return_value = 60
            print("WARNING: Could not connect to Redis, using mock Redis client instead")

    def tearDown(self):
        """Clean up after tests"""
        # Clean up Redis
        self.redis_client.delete(f"device:{self.device.id}")
        self.redis_client.delete(f"device:{self.device.id}:stat")

    def test_toggle_hide_view_real(self):
        """Test the toggle_hide view with real cache"""
        url = reverse("device_manager:toggle_hide", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Refresh the device from the database
        self.device.refresh_from_db()
        self.assertTrue(self.device.hidn)

        # Verify the device was cached in Redis
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        self.assertIsNotNone(cached_json)

        # Parse the JSON and verify the data
        cached_data = json.loads(cached_json)
        self.assertTrue(cached_data["hidn"])

        # Toggle again
        response = self.client.get(url)
        self.device.refresh_from_db()
        self.assertFalse(self.device.hidn)

        # Verify the cache was updated
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        cached_data = json.loads(cached_json)
        self.assertFalse(cached_data["hidn"])

    def test_toggle_maintenance_view_real(self):
        """Test the toggle_maintenance view with real cache"""
        url = reverse("device_manager:toggle_maintenance", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Refresh the device from the database
        self.device.refresh_from_db()
        self.assertTrue(self.device.mntc)

        # Verify the device was cached in Redis
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        self.assertIsNotNone(cached_json)

        # Parse the JSON and verify the data
        cached_data = json.loads(cached_json)
        self.assertTrue(cached_data["mntc"])

        # Toggle again
        response = self.client.get(url)
        self.device.refresh_from_db()
        self.assertFalse(self.device.mntc)

        # Verify the cache was updated
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        cached_data = json.loads(cached_json)
        self.assertFalse(cached_data["mntc"])

    def test_clear_status_view_real(self):
        """Test the clear_status view with real cache"""
        # First set the device to a non-default state
        self.device.stat = "Warning"
        self.device.actv = True
        self.device.attr["client"]["Motion event."] = True
        self.device.attr["client"]["Shock event."] = True
        self.device.attr["client"]["Motionless event."] = False
        self.device.save()

        url = reverse("device_manager:clear_status", args=[self.device.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Redirect

        # Refresh the device from the database
        self.device.refresh_from_db()
        self.assertEqual(self.device.stat, "Online")
        self.assertFalse(self.device.actv)
        self.assertFalse(self.device.attr["client"]["Motion event."])
        self.assertFalse(self.device.attr["client"]["Shock event."])
        self.assertTrue(self.device.attr["client"]["Motionless event."])

        # Verify the device was cached in Redis
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        self.assertIsNotNone(cached_json)

        # Parse the JSON and verify the data
        cached_data = json.loads(cached_json)
        self.assertEqual(cached_data["stat"], "Online")
        self.assertFalse(cached_data["actv"])
        self.assertFalse(cached_data["attr"]["client"]["Motion event."])
        self.assertFalse(cached_data["attr"]["client"]["Shock event."])
        self.assertTrue(cached_data["attr"]["client"]["Motionless event."])

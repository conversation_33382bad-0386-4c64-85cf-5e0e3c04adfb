from django.test import TestCase, tag
from unittest.mock import patch, MagicMock
import json
import redis

from device_manager.models import Device
from device_manager.utils.device_cache import DeviceCache
from fields.models import Field


@tag('cache', 'mock')
class DeviceCacheUtilsTestCase(TestCase):
    """Test cases for the device_cache utility functions with mocked Redis"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create a test device
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test Description",
            euid="ABCDEF0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        self.device_cache = DeviceCache()

    @patch('device_manager.utils.device_cache.r')
    def test_set_device_stat(self, mock_redis):
        """Test the set_device_stat function"""
        self.device_cache.set_device_stat(self.device.id, "Offline", ttl=60)
        mock_redis.set.assert_called_once_with(
            f"device:{self.device.id}:stat", "Offline", ex=60
        )

    @patch('device_manager.utils.device_cache.r')
    def test_get_device_stat(self, mock_redis):
        """Test the get_device_stat function"""
        mock_redis.get.return_value = "Offline"
        stat = self.device_cache.get_device_stat(self.device.id)
        self.assertEqual(stat, "Offline")
        mock_redis.get.assert_called_once_with(f"device:{self.device.id}:stat")

    @patch('device_manager.utils.device_cache.r')
    def test_cache_device(self, mock_redis):
        """Test the cache_device function"""
        self.device_cache.cache_device(self.device, ttl=60)
        # Check that redis.set was called twice (once for device, once for stat)
        self.assertEqual(mock_redis.set.call_count, 2)

    @patch('device_manager.utils.device_cache.r')
    def test_get_cached_device(self, mock_redis):
        """Test the get_cached_device function"""
        # Mock the redis get response
        mock_redis.get.return_value = self.device.to_json()

        # Call the function
        cached_device = self.device_cache.get_cached_device(self.device.id)

        # Verify the result
        self.assertIsNotNone(cached_device)
        self.assertEqual(cached_device.name, self.device.name)
        self.assertEqual(cached_device.euid, self.device.euid)

        # Verify redis.get was called with the correct key
        mock_redis.get.assert_called_once_with(f"device:{self.device.id}")


@tag('cache', 'unmock')
class UnmockedDeviceCacheUtilsTestCase(TestCase):
    """Test cases for the device_cache utility functions with real Redis"""

    def setUp(self):
        """Set up test data"""
        # Create a test field
        self.field = Field.objects.create(
            name="Test Field Unmocked",
            cord={"type": "Polygon", "coordinates": [[[0, 0], [0, 1], [1, 1], [1, 0], [0, 0]]]},
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create a test device
        self.device = Device.objects.create(
            name="Test Device Unmocked",
            desc="Test Description for Unmocked Tests",
            euid="UNMOCK0123456789",
            stat="Online",
            temp=25,
            batt=80,
            chrg=False,
            actv=True,
            mntc=False,
            hidn=False,
            attr={"client": {"temp": 25}},
            type="Whiskers Node V1",
            aset="Battery",
            loca={"lati": 0.0, "long": 0.0, "alti": 0.0, "oofi": False, "plac": "Outdoor"},
            fild=self.field,
            offp=10
        )

        # Connect to Redis using django-redis
        from django_redis import get_redis_connection

        try:
            # Get Redis connection from django-redis cache
            self.redis_client = get_redis_connection("default")
            # Test the connection
            self.redis_client.ping()
        except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError):
            # If connection fails, use a mock Redis client
            self.redis_client = MagicMock()
            self.redis_client.get.return_value = None
            self.redis_client.set.return_value = True
            self.redis_client.delete.return_value = True
            self.redis_client.ttl.return_value = 60
            print("WARNING: Could not connect to Redis, using mock Redis client instead")

        # Clear any existing test data
        self.redis_client.delete(f"device:{self.device.id}")
        self.redis_client.delete(f"device:{self.device.id}:stat")

    def tearDown(self):
        """Clean up after tests"""
        # Clean up Redis if it's not a mock
        if not isinstance(self.redis_client, MagicMock):
            try:
                self.redis_client.delete(f"device:{self.device.id}")
                self.redis_client.delete(f"device:{self.device.id}:stat")
            except (redis.exceptions.ConnectionError, redis.exceptions.TimeoutError):
                print("WARNING: Could not connect to Redis during tearDown")

    def test_set_device_stat_real(self):
        """Test the set_device_stat function with real Redis"""
        # Set the device status
        self.device_cache.set_device_stat(self.device.id, "Offline", ttl=60)

        # Verify the status was set in Redis
        stat = self.redis_client.get(f"device:{self.device.id}:stat")
        # Handle both string and bytes response
        if isinstance(stat, bytes):
            stat = stat.decode('utf-8')
        self.assertEqual(stat, "Offline")

        # Verify TTL is set
        ttl = self.redis_client.ttl(f"device:{self.device.id}:stat")
        # TTL might be -1 if no expiration is set, which is acceptable
        if ttl != -1:
            self.assertLessEqual(ttl, 60)
            self.assertGreater(ttl, 0)

    def test_get_device_stat_real(self):
        """Test the get_device_stat function with real Redis"""
        # Set a status directly in Redis
        self.redis_client.set(f"device:{self.device.id}:stat", "Warning", ex=60)

        # Get the status using the function
        stat = self.device_cache.get_device_stat(self.device.id)
        # Handle both string and bytes response
        if isinstance(stat, bytes):
            stat = stat.decode('utf-8')
        self.assertEqual(stat, "Warning")

    def test_cache_device_real(self):
        """Test the cache_device function with real Redis"""
        # Cache the device with explicit TTL
        self.device_cache.cache_device(self.device, ttl=60)

        # Verify the device was cached in Redis
        cached_json = self.redis_client.get(f"device:{self.device.id}")
        self.assertIsNotNone(cached_json)

        # Parse the JSON and verify the data
        cached_data = json.loads(cached_json)
        self.assertEqual(cached_data["name"], "Test Device Unmocked")
        self.assertEqual(cached_data["euid"], "UNMOCK0123456789")

        # Verify TTL is set for the device key
        device_ttl = self.redis_client.ttl(f"device:{self.device.id}")
        # TTL might be -1 if no expiration is set, which is acceptable
        if device_ttl != -1:
            self.assertLessEqual(device_ttl, 60)
            self.assertGreater(device_ttl, 0)

        # Verify TTL is set for the stat key
        stat_ttl = self.redis_client.ttl(f"device:{self.device.id}:stat")
        # TTL might be -1 if no expiration is set, which is acceptable
        if stat_ttl != -1:
            self.assertLessEqual(stat_ttl, 60)
            self.assertGreater(stat_ttl, 0)

    def test_get_cached_device_real(self):
        """Test the get_cached_device function with real Redis"""
        # Cache the device directly in Redis
        device_json = self.device.to_json()
        self.redis_client.set(f"device:{self.device.id}", device_json, ex=60)

        # Get the cached device using the function
        cached_device = self.device_cache.get_cached_device(self.device.id)

        # Verify the cached device
        self.assertIsNotNone(cached_device)
        self.assertEqual(cached_device.name, "Test Device Unmocked")
        self.assertEqual(cached_device.euid, "UNMOCK0123456789")

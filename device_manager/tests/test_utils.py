from django.test import TestCase, tag

from device_manager.models import get_device_attrs_template, get_location_template


@tag('utility', 'mock')
class DeviceUtilityFunctionsTestCase(TestCase):
    """Test cases for the utility functions in the Device model"""

    def test_get_device_attrs_template(self):
        """Test the get_device_attrs_template function"""
        # Test with a valid device type
        attrs = get_device_attrs_template("Whiskers Node V1")
        self.assertIsInstance(attrs, dict)
        self.assertIn("client", attrs)

        # Test with an invalid device type
        attrs = get_device_attrs_template("Invalid Type")
        self.assertEqual(attrs, {})

    def test_get_location_template(self):
        """Test the get_location_template function"""
        location = get_location_template()
        self.assertEqual(location["lati"], 0)
        self.assertEqual(location["long"], 0)
        self.assertEqual(location["alti"], 0)
        self.assertEqual(location["oofi"], False)
        self.assertEqual(location["plac"], "Outdoor")

# Device Manager Tests

This directory contains tests for the Device Manager app. The tests are organized into separate files based on the functionality they test.

## Test Files

- `test_models.py` - Tests for the Device model with both mocked and unmocked cache functions
- `test_utils.py` - Tests for utility functions in the Device model
- `test_cache.py` - Tests for device_cache utility functions with both mocked and unmocked Redis
- `test_views.py` - Tests for Device views with both mocked and unmocked cache functions

## Running Tests

Tests can be run using Django's standard test command. The tests will use the main database configuration with Django's built-in test database creation.

### Run All Tests

```bash
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests
```

### Run Specific Test Files

```bash
# Run model tests
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests.test_models

# Run utility function tests
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests.test_utils

# Run cache utility tests
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests.test_cache

# Run view tests
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests.test_views
```

### Run Tests with Specific Tags

Tests are tagged as either 'mock' or 'unmock' to indicate whether they use mocked dependencies or real ones.

```bash
# Run tests that use mocked dependencies
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests --tag=mock

# Run tests that use real dependencies (Redis, etc.)
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests --tag=unmock
```

You can also combine tags with specific test files:

```bash
# Run only mocked tests in the models file
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests.test_models --tag=mock

# Run only unmocked tests in the cache file
docker exec -it whiskershub-web-1 python manage.py test device_manager.tests.test_cache --tag=unmock
```

## Test Database

Django will automatically create and manage a test database based on your main database configuration. The test database will be created before tests run and destroyed after tests complete.

## Redis Settings

For tests that interact with Redis, the connection settings are read from Django settings. The tests will attempt to connect to Redis using the settings from the Django configuration, and if that fails, they will fall back to using a mock Redis client.
